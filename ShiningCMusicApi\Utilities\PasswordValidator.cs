namespace ShiningCMusicApi.Utilities
{
    public static class PasswordValidator
    {
        public static bool IsValidPassword(string password)
        {
            if (string.IsNullOrEmpty(password))
                return false;

            // Minimum length requirement
            if (password.Length < 8)
                return false;

            // Add more validation rules as needed:
            // - At least one uppercase letter
            // - At least one lowercase letter  
            // - At least one digit
            // - At least one special character
            
            return true;
        }

        public static string GetPasswordRequirements()
        {
            return "Password must be at least 8 characters long.";
        }
    }
}
