@page "/admin"
@using ShiningCMusicCommon.Models
@using ShiningCMusicApp.Services
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.Buttons
@using Syncfusion.Blazor.Inputs
@using Syncfusion.Blazor.Popups
@using Syncfusion.Blazor.DropDowns
@inject IUserApiService UserApi
@inject ISubjectApiService SubjectApi
@inject ILocationApiService LocationApi
@inject ITutorApiService TutorApi
@inject IStudentApiService StudentApi
@inject IJSRuntime JSRuntime
@attribute [Authorize(Roles = "Administrator")]

<PageTitle>Admin Management</PageTitle>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-3 mb-md-4 fs-3 fs-md-1">⚙️ <span class="d-none d-sm-inline">Admin Management</span><span class="d-sm-none">Admin</span></h1>
            
            @if (isLoading)
            {
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p>Loading admin data...</p>
                </div>
            }
            else
            {
                <!-- Users Section -->
                <div class="card mb-4">
                    <div class="card-header">
                        <div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center">
                            <h5 class="mb-2 mb-md-0">👥 Users</h5>
                            <div class="d-flex flex-column flex-sm-row gap-2 w-100 w-md-auto justify-content-md-end">
                                <button class="btn btn-primary" style="min-width: 150px;" @onclick="OpenCreateUserModal">
                                    <i class="fas fa-plus"></i>
                                    <span class="d-none d-sm-inline ms-1">Add New User</span>
                                    <span class="d-sm-none ms-1">Add</span>
                                </button>
                                <button class="btn btn-secondary" style="min-width: 150px;" @onclick="RefreshData">
                                    <i class="fas fa-refresh"></i>
                                    <span class="d-none d-sm-inline ms-1">Refresh</span>
                                    <span class="d-sm-none ms-1">Refresh</span>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-1 p-md-3">
                        <SfGrid DataSource="@users" AllowPaging="true" AllowSorting="true" AllowFiltering="true"
                                AllowResizing="true" Height="300" CssClass="mobile-grid">
                            <GridPageSettings PageSize="5"></GridPageSettings>
                            <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.FilterBar"></GridFilterSettings>
                            <GridColumns>
                                <GridColumn Field=@nameof(User.LoginName) HeaderText="Login Name" Width="100" IsPrimaryKey="true"></GridColumn>
                                <GridColumn Field=@nameof(User.UserName) HeaderText="User Name" Width="150"></GridColumn>
                                <GridColumn Field=@nameof(User.RoleDescription) HeaderText="Role" Width="120"></GridColumn>
                                <GridColumn Field=@nameof(User.Note) HeaderText="Note" Width="200"></GridColumn>
                                <GridColumn HeaderText="Actions" Width="150" AllowFiltering="false" AllowSorting="false">
                                    <Template>
                                        @{
                                            var user = (context as User);
                                        }
                                        <div class="d-flex gap-1">
                                            <button class="btn btn-outline-primary" style="width: 50%;" @onclick="() => OpenEditUserModal(user)">
                                                <i class="fas fa-edit"></i> Edit
                                            </button>
                                            <button class="btn btn-outline-danger" style="width: 50%;" @onclick="() => DeleteUser(user?.LoginName)">
                                                <i class="fas fa-trash"></i> Delete
                                            </button>
                                        </div>
                                    </Template>
                                </GridColumn>
                            </GridColumns>
                        </SfGrid>
                    </div>
                </div>

                <!-- User Roles Section -->
                <div class="card mb-4">
                    <div class="card-header">
                        <div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center">
                            <h5 class="mb-2 mb-md-0">🔐 <span class="d-none d-sm-inline">User Roles</span><span class="d-sm-none">Roles</span></h5>
                            <div class="d-flex flex-column flex-sm-row gap-2 w-100 w-md-auto justify-content-md-end">
                                <button class="btn btn-primary" style="min-width: 150px;" @onclick="OpenCreateUserRoleModal">
                                    <i class="fas fa-plus"></i>
                                    <span class="d-none d-sm-inline ms-1">Add New Role</span>
                                    <span class="d-sm-none ms-1">Add</span>
                                </button>
                                <button class="btn btn-secondary" style="min-width: 150px;" @onclick="RefreshData">
                                    <i class="fas fa-refresh"></i>
                                    <span class="d-none d-sm-inline ms-1">Refresh</span>
                                    <span class="d-sm-none ms-1">Refresh</span>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-1 p-md-3">
                        <SfGrid DataSource="@userRoles" AllowPaging="true" AllowSorting="true" AllowFiltering="true"
                                AllowResizing="true" Height="300" CssClass="mobile-grid">
                            <GridPageSettings PageSize="5"></GridPageSettings>
                            <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.FilterBar"></GridFilterSettings>
                            <GridColumns>
                                <GridColumn Field=@nameof(UserRole.ID) HeaderText="ID" Width="80" IsPrimaryKey="true"
                                           AllowFiltering="false"></GridColumn>
                                <GridColumn Field=@nameof(UserRole.Description) HeaderText="Description" Width="200"></GridColumn>
                                <GridColumn HeaderText="Actions" Width="150" AllowFiltering="false" AllowSorting="false">
                                    <Template>
                                        @{
                                            var role = (context as UserRole);
                                        }
                                        <div class="d-flex gap-1">
                                            <button class="btn btn-outline-primary" style="width: 50%;" @onclick="() => OpenEditUserRoleModal(role)">
                                                <i class="fas fa-edit"></i> Edit
                                            </button>
                                            <button class="btn btn-outline-danger" style="width: 50%;" @onclick="() => DeleteUserRole(role?.ID)">
                                                <i class="fas fa-trash"></i> Delete
                                            </button>
                                        </div>
                                    </Template>
                                </GridColumn>
                            </GridColumns>
                        </SfGrid>
                    </div>
                </div>

                <!-- Subjects Section -->
                <div class="card mb-4">
                    <div class="card-header">
                        <div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center">
                            <h5 class="mb-2 mb-md-0">📚 Subjects</h5>
                            <div class="d-flex flex-column flex-sm-row gap-2 w-100 w-md-auto justify-content-md-end">
                                <button class="btn btn-primary" style="min-width: 150px;" @onclick="OpenCreateSubjectModal">
                                    <i class="fas fa-plus"></i>
                                    <span class="d-none d-sm-inline ms-1">Add New Subject</span>
                                    <span class="d-sm-none ms-1">Add</span>
                                </button>
                                <button class="btn btn-secondary" style="min-width: 150px;" @onclick="RefreshData">
                                    <i class="fas fa-refresh"></i>
                                    <span class="d-none d-sm-inline ms-1">Refresh</span>
                                    <span class="d-sm-none ms-1">Refresh</span>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-1 p-md-3">
                        <SfGrid DataSource="@subjects" AllowPaging="true" AllowSorting="true" AllowFiltering="true"
                                AllowResizing="true" Height="300" CssClass="mobile-grid">
                            <GridPageSettings PageSize="5"></GridPageSettings>
                            <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.FilterBar"></GridFilterSettings>
                            <GridColumns>
                                <GridColumn Field=@nameof(Subject.SubjectId) HeaderText="ID" Width="80" IsPrimaryKey="true" 
                                           AllowFiltering="false"></GridColumn>
                                <GridColumn Field=@nameof(Subject.SubjectName) HeaderText="Subject Name" Width="200"></GridColumn>
                                <GridColumn HeaderText="Actions" Width="150" AllowFiltering="false" AllowSorting="false">
                                    <Template>
                                        @{
                                            var subject = (context as Subject);
                                        }
                                        <div class="d-flex gap-1">
                                            <button class="btn btn-outline-primary" style="width: 50%;" @onclick="() => OpenEditSubjectModal(subject)"
                                                    title="Edit">
                                                <i class="fas fa-edit"></i> Edit
                                            </button>
                                            <button class="btn btn-outline-danger" style="width: 50%;" @onclick="() => DeleteSubject(subject)"
                                                    title="Delete">
                                                <i class="fas fa-trash"></i> Delete
                                            </button>
                                        </div>
                                    </Template>
                                </GridColumn>
                            </GridColumns>
                        </SfGrid>
                    </div>
                </div>

                <!-- Locations Section -->
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center">
                            <h5 class="mb-2 mb-md-0">📍 Locations</h5>
                            <div class="d-flex flex-column flex-sm-row gap-2 w-100 w-md-auto justify-content-md-end">
                                <button class="btn btn-primary" style="min-width: 150px;" @onclick="OpenCreateLocationModal">
                                    <i class="fas fa-plus"></i>
                                    <span class="d-none d-sm-inline ms-1">Add New Location</span>
                                    <span class="d-sm-none ms-1">Add</span>
                                </button>
                                <button class="btn btn-secondary" style="min-width: 150px;" @onclick="RefreshData">
                                    <i class="fas fa-refresh"></i>
                                    <span class="d-none d-sm-inline ms-1">Refresh</span>
                                    <span class="d-sm-none ms-1">Refresh</span>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-1 p-md-3">
                        <SfGrid DataSource="@locations" AllowPaging="true" AllowSorting="true" AllowFiltering="true"
                                AllowResizing="true" Height="300" CssClass="mobile-grid">
                            <GridPageSettings PageSize="5"></GridPageSettings>
                            <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.FilterBar"></GridFilterSettings>
                            <GridColumns>
                                <GridColumn Field=@nameof(Location.LocationId) HeaderText="ID" Width="80" IsPrimaryKey="true" 
                                           AllowFiltering="false"></GridColumn>
                                <GridColumn Field=@nameof(Location.LocationName) HeaderText="Location Name" Width="200"></GridColumn>
                                <GridColumn HeaderText="Actions" Width="150" AllowFiltering="false" AllowSorting="false">
                                    <Template>
                                        @{
                                            var location = (context as Location);
                                        }
                                        <div class="d-flex gap-1">
                                            <button class="btn btn-outline-primary" style="width: 50%;" @onclick="() => OpenEditLocationModal(location)"
                                                    title="Edit">
                                                <i class="fas fa-edit"></i> Edit
                                            </button>
                                            <button class="btn btn-outline-danger" style="width: 50%;" @onclick="() => DeleteLocation(location)"
                                                    title="Delete">
                                                <i class="fas fa-trash"></i> Delete
                                            </button>
                                        </div>
                                    </Template>
                                </GridColumn>
                            </GridColumns>
                        </SfGrid>
                    </div>
                </div>
            }
        </div>
    </div>
</div>

<!-- User Modal -->
<SfDialog @bind-Visible="showUserModal" Header="@userModalTitle" Width="500px" Height="auto" IsModal="true"
          ShowCloseIcon="true" AllowDragging="true">
    <DialogTemplates>
        <Content>
            <EditForm Model="@currentUser" OnValidSubmit="@SaveUser">
                <DataAnnotationsValidator />
                <ValidationSummary class="text-danger" />

                <div class="mb-3">
                    <label class="form-label">Login Name <span class="text-danger">*</span></label>
                    <SfTextBox @bind-Value="currentUser.LoginName" Placeholder="Enter login name"
                               CssClass="form-control" Enabled="@(!isEditUserMode)"></SfTextBox>
                    <ValidationMessage For="@(() => currentUser.LoginName)" />
                </div>

                <div class="mb-3">
                    <label class="form-label">User Name</label>
                    <SfTextBox @bind-Value="currentUser.UserName" Placeholder="Enter user name"
                               CssClass="form-control"></SfTextBox>
                    <ValidationMessage For="@(() => currentUser.UserName)" />
                </div>

                <div class="mb-3">
                    <label class="form-label">Password</label>
                    <SfTextBox @bind-Value="currentUser.Password" Type="InputType.Password"
                               Placeholder="Enter password" CssClass="form-control"></SfTextBox>
                    <ValidationMessage For="@(() => currentUser.Password)" />
                </div>

                <div class="mb-3">
                    <label class="form-label">Role</label>
                    <SfDropDownList TValue="int?" TItem="UserRole" @bind-Value="currentUser.RoleId"
                                    DataSource="@userRoles" CssClass="form-control" Placeholder="Select a role">
                        <DropDownListFieldSettings Value="ID" Text="Description"></DropDownListFieldSettings>
                        <DropDownListEvents TValue="int?" TItem="UserRole" ValueChange="@OnRoleChanged"></DropDownListEvents>
                    </SfDropDownList>
                    <ValidationMessage For="@(() => currentUser.RoleId)" />
                </div>

                @if (!isEditUserMode && showAssignmentSection)
                {
                    <div class="mb-3">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">👤 Assign to Person</h6>
                                <small class="text-muted">Link this user account to an existing tutor or student</small>
                            </div>
                            <div class="card-body">
                                @if (GetSelectedRoleDescription() == "Tutor")
                                {
                                    <div class="mb-3">
                                        <label class="form-label">Select Tutor</label>
                                        <SfDropDownList TValue="int?" TItem="Tutor" @bind-Value="selectedTutorId"
                                                        DataSource="@GetAvailableTutors()" CssClass="form-control"
                                                        Placeholder="Select a tutor to link">
                                            <DropDownListFieldSettings Value="TutorId" Text="TutorName"></DropDownListFieldSettings>
                                        </SfDropDownList>
                                        <small class="form-text text-muted">This will update the tutor's login name to match this user</small>
                                    </div>
                                }
                                else if (GetSelectedRoleDescription() == "Student")
                                {
                                    <div class="mb-3">
                                        <label class="form-label">Select Student</label>
                                        <SfDropDownList TValue="int?" TItem="Student" @bind-Value="selectedStudentId"
                                                        DataSource="@GetAvailableStudents()" CssClass="form-control"
                                                        Placeholder="Select a student to link">
                                            <DropDownListFieldSettings Value="StudentId" Text="StudentName"></DropDownListFieldSettings>
                                        </SfDropDownList>
                                        <small class="form-text text-muted">This will update the student's login name to match this user</small>
                                    </div>
                                }
                            </div>
                        </div>
                    </div>
                }

                <div class="mb-3">
                    <label class="form-label">Note</label>
                    <SfTextBox @bind-Value="currentUser.Note" Placeholder="Enter note"
                               CssClass="form-control"></SfTextBox>
                    <ValidationMessage For="@(() => currentUser.Note)" />
                </div>

                <div class="d-flex justify-content-end gap-2 mt-4">
                    <SfButton CssClass="btn btn-blue-custom" type="submit" Disabled="@isSaving">
                        @if (isSaving)
                        {
                            <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                        }
                        @(isEditUserMode ? "Update" : "Create")
                    </SfButton>
                    <SfButton CssClass="btn btn-cancel-custom" @onclick="CloseUserModal">Cancel</SfButton>
                </div>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>

<!-- UserRole Modal -->
<SfDialog @bind-Visible="showUserRoleModal" Header="@userRoleModalTitle" Width="400px" Height="auto" IsModal="true"
          ShowCloseIcon="true" AllowDragging="true">
    <DialogTemplates>
        <Content>
            <EditForm Model="@currentUserRole" OnValidSubmit="@SaveUserRole">
                <DataAnnotationsValidator />
                <ValidationSummary class="text-danger" />

                <div class="mb-3">
                    <label class="form-label">Description <span class="text-danger">*</span></label>
                    <SfTextBox @bind-Value="currentUserRole.Description" Placeholder="Enter role description"
                               CssClass="form-control"></SfTextBox>
                    <ValidationMessage For="@(() => currentUserRole.Description)" />
                </div>

                <div class="d-flex justify-content-end gap-2 mt-4">
                    <SfButton CssClass="btn btn-blue-custom" type="submit" Disabled="@isSaving">
                        @if (isSaving)
                        {
                            <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                        }
                        @(isEditUserRoleMode ? "Update" : "Create")
                    </SfButton>
                    <SfButton CssClass="btn btn-cancel-custom" @onclick="CloseUserRoleModal">Cancel</SfButton>
                </div>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>

<!-- Subject Modal -->
<SfDialog @bind-Visible="showSubjectModal" Header="@subjectModalTitle" Width="400px" Height="auto" IsModal="true" 
          ShowCloseIcon="true" AllowDragging="true">
    <DialogTemplates>
        <Content>
            <EditForm Model="@currentSubject" OnValidSubmit="@SaveSubject">
                <DataAnnotationsValidator />
                <ValidationSummary class="text-danger" />
                
                <div class="mb-3">
                    <label class="form-label">Subject Name <span class="text-danger">*</span></label>
                    <SfTextBox @bind-Value="currentSubject.SubjectName" Placeholder="Enter subject name" 
                               CssClass="form-control"></SfTextBox>
                    <ValidationMessage For="@(() => currentSubject.SubjectName)" />
                </div>

                <div class="d-flex justify-content-end gap-2 mt-4">
                    <SfButton CssClass="btn btn-blue-custom" type="submit" Disabled="@isSaving">
                        @if (isSaving)
                        {
                            <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                        }
                        @(isEditSubjectMode ? "Update" : "Create")
                    </SfButton>
                    <SfButton CssClass="btn btn-cancel-custom" @onclick="CloseSubjectModal">Cancel</SfButton>
                </div>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>

<!-- Location Modal -->
<SfDialog @bind-Visible="showLocationModal" Header="@locationModalTitle" Width="400px" Height="auto" IsModal="true" 
          ShowCloseIcon="true" AllowDragging="true">
    <DialogTemplates>
        <Content>
            <EditForm Model="@currentLocation" OnValidSubmit="@SaveLocation">
                <DataAnnotationsValidator />
                <ValidationSummary class="text-danger" />
                
                <div class="mb-3">
                    <label class="form-label">Location Name <span class="text-danger">*</span></label>
                    <SfTextBox @bind-Value="currentLocation.LocationName" Placeholder="Enter location name" 
                               CssClass="form-control"></SfTextBox>
                    <ValidationMessage For="@(() => currentLocation.LocationName)" />
                </div>

                <div class="d-flex justify-content-end gap-2 mt-4">
                    <SfButton CssClass="btn btn-blue-custom" type="submit" Disabled="@isSaving">
                        @if (isSaving)
                        {
                            <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                        }
                        @(isEditLocationMode ? "Update" : "Create")
                    </SfButton>
                    <SfButton CssClass="btn btn-cancel-custom" @onclick="CloseLocationModal">Cancel</SfButton>
                </div>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>

@code {
    private List<User> users = new();
    private List<UserRole> userRoles = new();
    private List<Subject> subjects = new();
    private List<Location> locations = new();
    private List<Tutor> tutors = new();
    private List<Student> students = new();
    private bool isLoading = true;

    // User modal variables
    private bool showUserModal = false;
    private bool isEditUserMode = false;
    private string userModalTitle = "";
    private User currentUser = new();
    private int? selectedTutorId = null;
    private int? selectedStudentId = null;
    private bool showAssignmentSection = false;

    // UserRole modal variables
    private bool showUserRoleModal = false;
    private bool isEditUserRoleMode = false;
    private string userRoleModalTitle = "";
    private UserRole currentUserRole = new();

    // Subject modal variables
    private bool showSubjectModal = false;
    private bool isEditSubjectMode = false;
    private bool isSaving = false;
    private string subjectModalTitle = "";
    private Subject currentSubject = new();
    
    // Location modal variables
    private bool showLocationModal = false;
    private bool isEditLocationMode = false;
    private string locationModalTitle = "";
    private Location currentLocation = new();

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    private async Task LoadData()
    {
        isLoading = true;
        try
        {
            var usersTask = UserApi.GetUsersAsync();
            var userRolesTask = UserApi.GetUserRolesAsync();
            var subjectsTask = SubjectApi.GetSubjectsAsync();
            var locationsTask = LocationApi.GetLocationsAsync();
            var tutorsTask = TutorApi.GetTutorsAsync();
            var studentsTask = StudentApi.GetStudentsAsync();

            await Task.WhenAll(usersTask, userRolesTask, subjectsTask, locationsTask, tutorsTask, studentsTask);

            users = await usersTask;
            userRoles = await userRolesTask;
            subjects = await subjectsTask;
            locations = await locationsTask;
            tutors = await tutorsTask;
            students = await studentsTask;

            await JSRuntime.InvokeVoidAsync("console.log", $"Loaded {users.Count} users, {userRoles.Count} roles, {subjects.Count} subjects and {locations.Count} locations");
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error loading data: {ex.Message}");
            await JSRuntime.InvokeVoidAsync("alert", $"Error loading data: {ex.Message}");
        }
        finally
        {
            isLoading = false;
        }
    }

    // Subject CRUD Methods
    private void OpenCreateSubjectModal()
    {
        currentSubject = new Subject();
        isEditSubjectMode = false;
        subjectModalTitle = "Create New Subject";
        showSubjectModal = true;
    }

    private async Task RefreshData()
    {
        await LoadData();
    }

    private void OpenEditSubjectModal(Subject? subject)
    {
        if (subject != null)
        {
            currentSubject = new Subject
            {
                SubjectId = subject.SubjectId,
                SubjectName = subject.SubjectName
            };

            isEditSubjectMode = true;
            subjectModalTitle = "Edit Subject";
            showSubjectModal = true;
        }
    }

    private void CloseSubjectModal()
    {
        showSubjectModal = false;
        currentSubject = new();
        isSaving = false;
    }

    private async Task SaveSubject()
    {
        if (string.IsNullOrWhiteSpace(currentSubject.SubjectName))
        {
            await JSRuntime.InvokeVoidAsync("alert", "Subject name is required.");
            return;
        }

        isSaving = true;
        try
        {
            bool success;
            if (isEditSubjectMode)
            {
                success = await SubjectApi.UpdateSubjectAsync(currentSubject.SubjectId, currentSubject);
                // if (success)
                // {
                //     await JSRuntime.InvokeVoidAsync("alert", "Subject updated successfully!");
                // }
            }
            else
            {
                var createdSubject = await SubjectApi.CreateSubjectAsync(currentSubject);
                success = createdSubject != null;
                // if (success)
                // {
                //     await JSRuntime.InvokeVoidAsync("alert", "Subject created successfully!");
                // }
            }

            if (success)
            {
                CloseSubjectModal();
                await LoadData();
            }
            else
            {
                await JSRuntime.InvokeVoidAsync("alert", "Failed to save subject. Please try again.");
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error saving subject: {ex.Message}");
            await JSRuntime.InvokeVoidAsync("alert", $"Error saving subject: {ex.Message}");
        }
        finally
        {
            isSaving = false;
        }
    }

    private async Task DeleteSubject(Subject? subject)
    {
        if (subject == null) return;

        var confirmed = await JSRuntime.InvokeAsync<bool>("confirm", 
            $"Are you sure you want to delete subject '{subject.SubjectName}'? This action cannot be undone.");
        
        if (confirmed)
        {
            try
            {
                var success = await SubjectApi.DeleteSubjectAsync(subject.SubjectId);
                if (success)
                {
                    // await JSRuntime.InvokeVoidAsync("alert", "Subject deleted successfully!");
                    await LoadData();
                }
                else
                {
                    await JSRuntime.InvokeVoidAsync("alert", "Failed to delete subject. Please try again.");
                }
            }
            catch (Exception ex)
            {
                await JSRuntime.InvokeVoidAsync("console.error", $"Error deleting subject: {ex.Message}");
                await JSRuntime.InvokeVoidAsync("alert", $"Error deleting subject: {ex.Message}");
            }
        }
    }

    // Location CRUD Methods
    private void OpenCreateLocationModal()
    {
        currentLocation = new Location();
        isEditLocationMode = false;
        locationModalTitle = "Create New Location";
        showLocationModal = true;
    }

    private void OpenEditLocationModal(Location? location)
    {
        if (location != null)
        {
            currentLocation = new Location
            {
                LocationId = location.LocationId,
                LocationName = location.LocationName
            };

            isEditLocationMode = true;
            locationModalTitle = "Edit Location";
            showLocationModal = true;
        }
    }

    private void CloseLocationModal()
    {
        showLocationModal = false;
        currentLocation = new();
        isSaving = false;
    }

    private async Task SaveLocation()
    {
        if (string.IsNullOrWhiteSpace(currentLocation.LocationName))
        {
            await JSRuntime.InvokeVoidAsync("alert", "Location name is required.");
            return;
        }

        isSaving = true;
        try
        {
            bool success;
            if (isEditLocationMode)
            {
                success = await LocationApi.UpdateLocationAsync(currentLocation.LocationId, currentLocation);
                if (success)
                {
                    await JSRuntime.InvokeVoidAsync("alert", "Location updated successfully!");
                }
            }
            else
            {
                var createdLocation = await LocationApi.CreateLocationAsync(currentLocation);
                success = createdLocation != null;
                if (success)
                {
                    await JSRuntime.InvokeVoidAsync("alert", "Location created successfully!");
                }
            }

            if (success)
            {
                CloseLocationModal();
                await LoadData();
            }
            else
            {
                await JSRuntime.InvokeVoidAsync("alert", "Failed to save location. Please try again.");
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error saving location: {ex.Message}");
            await JSRuntime.InvokeVoidAsync("alert", $"Error saving location: {ex.Message}");
        }
        finally
        {
            isSaving = false;
        }
    }

    private async Task DeleteLocation(Location? location)
    {
        if (location == null) return;

        var confirmed = await JSRuntime.InvokeAsync<bool>("confirm",
            $"Are you sure you want to delete location '{location.LocationName}'? This action cannot be undone.");

        if (confirmed)
        {
            try
            {
                var success = await LocationApi.DeleteLocationAsync(location.LocationId);
                if (success)
                {
                    await JSRuntime.InvokeVoidAsync("alert", "Location deleted successfully!");
                    await LoadData();
                }
                else
                {
                    await JSRuntime.InvokeVoidAsync("alert", "Failed to delete location. Please try again.");
                }
            }
            catch (Exception ex)
            {
                await JSRuntime.InvokeVoidAsync("console.error", $"Error deleting location: {ex.Message}");
                await JSRuntime.InvokeVoidAsync("alert", $"Error deleting location: {ex.Message}");
            }
        }
    }

    // User Management Methods
    private void OpenCreateUserModal()
    {
        currentUser = new User();
        selectedTutorId = null;
        selectedStudentId = null;
        showAssignmentSection = false;
        isEditUserMode = false;
        userModalTitle = "Create New User";
        showUserModal = true;
    }

    private void OpenEditUserModal(User? user)
    {
        if (user != null)
        {
            currentUser = new User
            {
                LoginName = user.LoginName,
                UserName = user.UserName,
                Password = user.Password,
                Note = user.Note,
                RoleId = user.RoleId
            };
            isEditUserMode = true;
            userModalTitle = "Edit User";
            showUserModal = true;
        }
    }

    private void CloseUserModal()
    {
        showUserModal = false;
        currentUser = new();
        selectedTutorId = null;
        selectedStudentId = null;
        showAssignmentSection = false;
        isSaving = false;
    }

    private void OnRoleChanged(ChangeEventArgs<int?, UserRole> args)
    {
        if (!isEditUserMode)
        {
            var roleDescription = GetSelectedRoleDescription();
            showAssignmentSection = roleDescription == "Tutor" || roleDescription == "Student";
            selectedTutorId = null;
            selectedStudentId = null;
        }
    }

    private string GetSelectedRoleDescription()
    {
        if (currentUser.RoleId.HasValue)
        {
            var role = userRoles.FirstOrDefault(r => r.ID == currentUser.RoleId.Value);
            return role?.Description ?? "";
        }
        return "";
    }

    private List<Tutor> GetAvailableTutors()
    {
        // Return tutors that don't have a login name assigned yet
        return tutors.Where(t => string.IsNullOrEmpty(t.LoginName)).ToList();
    }

    private List<Student> GetAvailableStudents()
    {
        // Return students that don't have a login name assigned yet
        return students.Where(s => string.IsNullOrEmpty(s.LoginName)).ToList();
    }

    private async Task SaveUser()
    {
        isSaving = true;
        try
        {
            if (isEditUserMode)
            {
                await UserApi.UpdateUserAsync(currentUser.LoginName, currentUser);
                await JSRuntime.InvokeVoidAsync("alert", "User updated successfully!");
            }
            else
            {
                // Create the user first
                var createdUser = await UserApi.CreateUserAsync(currentUser);
                if (createdUser != null)
                {
                    // Handle assignment to tutor or student
                    await HandleUserAssignment();
                    await JSRuntime.InvokeVoidAsync("alert", "User created and assigned successfully!");
                }
                else
                {
                    await JSRuntime.InvokeVoidAsync("alert", "Failed to create user. Please try again.");
                    return;
                }
            }

            CloseUserModal();
            await LoadData();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error saving user: {ex.Message}");
            await JSRuntime.InvokeVoidAsync("alert", $"Error saving user: {ex.Message}");
        }
        finally
        {
            isSaving = false;
        }
    }

    private async Task HandleUserAssignment()
    {
        try
        {
            if (selectedTutorId.HasValue)
            {
                // Update the tutor's login name
                var tutor = tutors.FirstOrDefault(t => t.TutorId == selectedTutorId.Value);
                if (tutor != null)
                {
                    tutor.LoginName = currentUser.LoginName;
                    await TutorApi.UpdateTutorAsync(tutor.TutorId, tutor);
                }
            }
            else if (selectedStudentId.HasValue)
            {
                // Update the student's login name
                var student = students.FirstOrDefault(s => s.StudentId == selectedStudentId.Value);
                if (student != null)
                {
                    student.LoginName = currentUser.LoginName;
                    await StudentApi.UpdateStudentAsync(student.StudentId, student);
                }
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error assigning user: {ex.Message}");
            // Don't throw here as the user was already created successfully
        }
    }

    private async Task DeleteUser(string? loginName)
    {
        if (!string.IsNullOrEmpty(loginName))
        {
            var confirmed = await JSRuntime.InvokeAsync<bool>("confirm", $"Are you sure you want to delete user '{loginName}'?");
            if (!confirmed) return;

            try
            {
                await UserApi.DeleteUserAsync(loginName);
                await JSRuntime.InvokeVoidAsync("alert", "User deleted successfully!");
                await LoadData();
            }
            catch (Exception ex)
            {
                await JSRuntime.InvokeVoidAsync("console.error", $"Error deleting user: {ex.Message}");
                await JSRuntime.InvokeVoidAsync("alert", $"Error deleting user: {ex.Message}");
            }
        }
    }

    // UserRole Management Methods
    private void OpenCreateUserRoleModal()
    {
        currentUserRole = new UserRole();
        isEditUserRoleMode = false;
        userRoleModalTitle = "Create New User Role";
        showUserRoleModal = true;
    }

    private void OpenEditUserRoleModal(UserRole? role)
    {
        if (role != null)
        {
            currentUserRole = new UserRole
            {
                ID = role.ID,
                Description = role.Description
            };
            isEditUserRoleMode = true;
            userRoleModalTitle = "Edit User Role";
            showUserRoleModal = true;
        }
    }

    private void CloseUserRoleModal()
    {
        showUserRoleModal = false;
        currentUserRole = new();
        isSaving = false;
    }

    private async Task SaveUserRole()
    {
        isSaving = true;
        try
        {
            if (isEditUserRoleMode)
            {
                await UserApi.UpdateUserRoleAsync(currentUserRole);
                await JSRuntime.InvokeVoidAsync("alert", "User role updated successfully!");
            }
            else
            {
                await UserApi.CreateUserRoleAsync(currentUserRole);
                await JSRuntime.InvokeVoidAsync("alert", "User role created successfully!");
            }

            CloseUserRoleModal();
            await LoadData();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error saving user role: {ex.Message}");
            await JSRuntime.InvokeVoidAsync("alert", $"Error saving user role: {ex.Message}");
        }
        finally
        {
            isSaving = false;
        }
    }

    private async Task DeleteUserRole(int? roleId)
    {
        if (roleId.HasValue)
        {
            var confirmed = await JSRuntime.InvokeAsync<bool>("confirm", $"Are you sure you want to delete this user role?");
            if (!confirmed) return;

            try
            {
                await UserApi.DeleteUserRoleAsync(roleId.Value);
                await JSRuntime.InvokeVoidAsync("alert", "User role deleted successfully!");
                await LoadData();
            }
            catch (Exception ex)
            {
                await JSRuntime.InvokeVoidAsync("console.error", $"Error deleting user role: {ex.Message}");
                await JSRuntime.InvokeVoidAsync("alert", $"Error deleting user role: {ex.Message}");
            }
        }
    }
}
